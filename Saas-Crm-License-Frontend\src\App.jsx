import React, { useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import AdminLayout from '@saas-crm/shared/AdminLayout';
import { checkAuthAndRedirect } from '@saas-crm/shared/tokenHandler';
import LicensesPage from './pages/LicensesPage';
import PackagesPage from './pages/PackagesPage';
import SubscriptionsPage from './pages/SubscriptionsPage';
import SubscriptionRequestsPage from './pages/SubscriptionRequestsPage';


function App() {
    console.log('Saas-Crm-License-Frontend App loaded');
    useEffect(() => {
        checkAuthAndRedirect();
        // console.log('tenant')
    }, []);
    return (
        <div className="App">
            <BrowserRouter basename='subscription'>
                <AdminLayout>
                    <Routes>
                        <Route path="/licenses" element={<LicensesPage />} />
                        <Route path="/packages" element={<PackagesPage />} />
                        <Route path="/subscriptions" element={<SubscriptionsPage />} />
                        <Route path="/subscription-requests" element={<SubscriptionRequestsPage />} />
                    </Routes>
                </AdminLayout>
            </BrowserRouter>
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        theme: {
                            primary: 'green',
                            secondary: 'black',
                        },
                    },
                    error: {
                        duration: 5000,
                        theme: {
                            primary: 'red',
                            secondary: 'black',
                        },
                    },
                }}
            />
        </div>
    );
}

export default App;
