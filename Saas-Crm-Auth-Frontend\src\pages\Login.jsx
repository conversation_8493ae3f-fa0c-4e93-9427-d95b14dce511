import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Mail, Lock, LogIn, Key } from 'lucide-react';

import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardFooter } from '../components/ui/card';
import { Checkbox } from '../components/ui/checkbox';
import { Alert, AlertDescription } from '../components/ui/alert';
import apiClient from '@/lib/apiClient';



const loginSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Please enter a valid email address'),
    password: z
        .string()
        .min(1, 'Password is required')
        .min(6, 'Password must be at least 6 characters'),
    rememberMe: z.boolean().optional(),
    licenseKey: z.string().optional(),
});

const Login = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [formError, setFormError] = useState('');

    const {
        register,
        handleSubmit,
        control,
        formState: { errors, isSubmitting },
        setError,
    } = useForm({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
            rememberMe: false,
            licenseKey: '',
        },
    });

    // const onSubmit = async ({ email, password, rememberMe }) => {
    //     setFormError('');

    //     try {
    //         apiClient.post('/auth/login', {
    //             email,
    //             password,
    //             remember_me: rememberMe,
    //             licenseKey: "DEMO-LICENSE-KEY-123"
    //         })

    //         const response = await fetch('http://192.168.88.71:3000/api/auth/login', {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //             },
    //             body: JSON.stringify({
    //                 email,
    //                 password,
    //                 remember_me: rememberMe,
    //                 licenseKey: "DEMO-LICENSE-KEY-123"
    //             }),
    //         });

    //         const data = await response.json();
    //         if (response.ok && data.access_token) {
    //             localStorage.setItem('token', data.access_token);
    //             localStorage.setItem('tenantId', data.user?.tenant_id);
    //             localStorage.setItem('userId', data.user?.id);
    //             localStorage.setItem('name', data.user?.name);

    //             if (data.user) {
    //                 localStorage.setItem('user', JSON.stringify(data.user));
    //             }

    //             const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 'http://localhost:3002/dashboard';
    //             const decodedUrl = decodeURIComponent(redirectUrl);
    //             const separator = decodedUrl.includes('?') ? '&' : '?';
    //             window.location.href = decodedUrl + separator + 'token=' + data.access_token + '&tenantId=' + data.user?.tenant_id + '&userId=' + data.user?.id + '&name=' + data.user?.name;
    //         } else {
    //             if (data.error?.includes('email')) {
    //                 setError('email', { message: data.error });
    //             } else if (data.error?.includes('password')) {
    //                 setError('password', { message: data.error });
    //             } else {
    //                 setFormError(data.message || data.error || 'Login failed. Please try again.');
    //             }
    //         }
    //     } catch (error) {
    //         console.error('Login error:', error);
    //         setFormError('An unexpected error occurred.');
    //     }
    // };
    const onSubmit = async ({ email, password, rememberMe, licenseKey }) => {
        setFormError('');

        try {
            const { data } = await apiClient.post('/auth/login', {
                email,
                password,
                remember_me: rememberMe,
                licenseKey: licenseKey,
            });

            if (data?.access_token) {
                localStorage.setItem('token', data.access_token);
                localStorage.setItem('tenantId', data.user?.tenant_id);
                localStorage.setItem('userId', data.user?.id);
                localStorage.setItem('name', data.user?.name);
                localStorage.setItem('role', data.user?.role);

                if (data.user) {
                    localStorage.setItem('user', JSON.stringify(data.user));
                }

                const redirectUrl = import.meta.env.VITE_LOGIN_REDIRECT

                const decodedUrl = decodeURIComponent(redirectUrl);
                const separator = decodedUrl.includes('?') ? '&' : '?';

                window.location.href =
                    `${decodedUrl}${separator}token=${data.access_token}` +
                    `&tenantId=${data.user?.tenant_id}` +
                    `&userId=${data.user?.id}` +
                    `&name=${encodeURIComponent(data.user?.name)}` +
                    `&role=${encodeURIComponent(data.user?.role)}`;
            } else {
                if (data.error?.includes('email')) {
                    setError('email', { message: data.error });
                } else if (data.error?.includes('password')) {
                    setError('password', { message: data.error });
                } else {
                    setFormError(data.message || data.error || 'Login failed. Please try again.');
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            setFormError(error.response?.data?.message || 'An unexpected error occurred.');
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold bg-gradient-to-r text-accent bg-clip-text">
                        Accord
                    </h1>
                    <p className="mt-2 text-sm text-gray-600">
                        Welcome back! Please sign in to your account.
                    </p>
                </div>

                <Card className="mt-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <CardContent className="space-y-6">
                            {formError && (
                                <Alert variant="destructive" className="border-red-200 bg-red-50">
                                    <AlertDescription className="text-red-800">
                                        {formError}
                                    </AlertDescription>
                                </Alert>
                            )}

                            {/* Email */}
                            <div className="space-y-2">
                                <Label htmlFor="email">Email Address</Label>
                                <div className="relative">
                                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        className="pl-10 h-11"
                                        {...register('email')}
                                        disabled={isSubmitting}
                                    />
                                </div>
                                {errors.email && (
                                    <p className="text-sm text-red-600">{errors.email.message}</p>
                                )}
                            </div>

                            {/* Password */}
                            <div className="space-y-2">
                                <Label htmlFor="password">Password</Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="Enter your password"
                                        className="pl-10 pr-10 h-11"
                                        {...register('password')}
                                        disabled={isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                                        onClick={() => setShowPassword((prev) => !prev)}
                                        disabled={isSubmitting}
                                    >
                                        {showPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {errors.password && (
                                    <p className="text-sm text-red-600">{errors.password.message}</p>
                                )}
                            </div>

                            {/* License Key */}
                            <div className="space-y-2">
                                <Label htmlFor="licenseKey">License Key</Label>
                                <div className="relative">
                                    <Key className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="licenseKey"
                                        type="text"
                                        placeholder="Enter your license key"
                                        className="pl-10 h-11"
                                        {...register('licenseKey')}
                                        disabled={isSubmitting}
                                    />
                                </div>
                                {errors.licenseKey && (
                                    <p className="text-sm text-red-600">{errors.licenseKey.message}</p>
                                )}
                            </div>

                            {/* Remember Me */}
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <Controller
                                        name="rememberMe"
                                        control={control}
                                        render={({ field }) => (
                                            <Checkbox
                                                id="rememberMe"
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                                disabled={isSubmitting}
                                            />
                                        )}
                                    />
                                    <Label htmlFor="rememberMe" className="text-sm">
                                        Remember me
                                    </Label>
                                </div>
                                <Link
                                    to="/forgot-password"
                                    className="text-sm text-accent hover:text-primary font-medium"
                                >
                                    Forgot password?
                                </Link>
                            </div>
                        </CardContent>

                        {/* Submit */}
                        <CardFooter className="flex flex-col space-y-4 pt-6">
                            <Button
                                type="submit"
                                className="w-full h-11 bg-accent hover:bg-primary"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Signing In...
                                    </>
                                ) : (
                                    <>
                                        <LogIn className="mr-2 h-4 w-4" />
                                        Sign In
                                    </>
                                )}
                            </Button>

                            {import.meta.env.VITE_ENABLE_REGISTRATION === 'true' && (
                                <div className="text-center text-sm">
                                    <span className="text-gray-600">Don't have an account? </span>
                                    <Link
                                        to="/register"
                                        className="text-primary hover:text-primary-dark font-medium"

                                    >
                                        Sign up here
                                    </Link>
                                </div>
                            )}
                        </CardFooter>
                    </form>
                </Card>

                {/* <div className="text-center text-xs text-gray-500">
                    <p>
                        By signing in, you agree to our{' '}
                        <Link to="/terms" className="text-blue-600 hover:text-blue-800">
                            Terms of Service
                        </Link>{' '}
                        and{' '}
                        <Link to="/privacy" className="text-blue-600 hover:text-blue-800">
                            Privacy Policy
                        </Link>
                    </p>
                </div> */}
            </div>
        </div>
    );
};

export default Login;
