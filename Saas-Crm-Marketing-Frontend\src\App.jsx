import React, { useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import UserPage from './pages/CampaignPage';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import AdminLayout from '@saas-crm/shared/AdminLayout';
import { checkAuthAndRedirect } from '@saas-crm/shared/tokenHandler';
import EmailTemplatePage from './pages/EmailTemplatePage';
import CampaignPage from './pages/CampaignPage';

function App() {
    useEffect(() => {
        checkAuthAndRedirect();
        // console.log('marketing')
    }, []);
    return (
        <div className="App">
            <BrowserRouter basename='marketing'>
                <AdminLayout>
                    <Routes>
                        <Route path="/email-templates" element={<EmailTemplatePage />} />
                        <Route path="/campaigns" element={<CampaignPage />} />
                    </Routes>
                </AdminLayout>
            </BrowserRouter>
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        theme: {
                            primary: 'green',
                            secondary: 'black',
                        },
                    },
                    error: {
                        duration: 5000,
                        theme: {
                            primary: 'red',
                            secondary: 'black',
                        },
                    },
                }}
            />
        </div>
    );
}

export default App;
